package
{
   import com.junkbyte.console.Cc;
   import flash.display.Loader;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.media.SoundMixer;
   import flash.media.SoundTransform;
   
   public class Doc extends MovieClip
   {
      private var l:Loader;
      
      public var BagProxy:Class;
      
      public var GoodConfig:Class;
      
      private var classCache:Object = {};
      
      private var _GamingClass:Class;
      
      private var _GiftAdditClass:Class;
      
      private var _GiftAddDefineClass:Class;
      
      private var _PlayerDataClass:Class;
      
      private var _ArmsDataClass:Class;
      
      private var _EquipDataClass:Class;
      
      private var _ThingsDataClass:Class;
      
      private var _GameArmsCtrlClass:Class;
      
      private var _UIShowClass:Class;
      
      private var _UIOrderClass:Class;
      
      private var gameFullyLoaded:Boolean = false;
      
      public function Doc()
      {
         super();
         if(stage)
         {
            this.init();
         }
         else
         {
            this.addEventListener(Event.ADDED_TO_STAGE,this.init);
         }
      }
      
      private function init(param1:Event = null) : void
      {
         this.l = new Loader();
         this.l.contentLoaderInfo.addEventListener(Event.COMPLETE,this.l_complete);
         this.l.loadBytes(new MySWFClass());
      }
      
      private function l_complete(param1:Event) : void
      {
         this.addChild(this.l);
         Cc.startOnStage(this,"");
         Cc.visible = true;
         Cc.commandLine = true;
         Cc.config.commandLineAllowed = true;
         this.preloadAllClasses();
         Cc.addSlashCommand("CreateGoods",this.create_goods,"刷道具 - 用法: /CreateGoods 道具名 数量");
         Cc.addSlashCommand("Music",this.sound_s,"静音");
      }
      
      private function create_goods(param1:String) : void
      {
         var _loc2_:Array;
         var _loc3_:*;
         var _loc4_:*;
         var GiftAddDefineClass:Class;
         var GiftAdditClass:Class;
         var giftDefine:*;
         var GoodsDataClass:Class;
         var GoodsDefineClass:Class;
         var GoodsAdditClass:Class;
         var goodsData:*;
         var goodsDefine:*;
         var playerData:*;
         try
         {
            _loc2_ = this.parseParams(param1);
            _loc3_ = _loc2_[0];
            _loc4_ = _loc2_[1];
            GiftAddDefineClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.define.GiftAddDefine") as Class;
            GiftAdditClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.GiftAddit") as Class;
            if(GiftAddDefineClass && GiftAdditClass)
            {
               giftDefine = new GiftAddDefineClass();
               giftDefine.inData_byStr("things;" + _loc3_ + ";" + _loc4_);
               GiftAdditClass.addByDefine(giftDefine,this.getPlayerData());
               Cc.log("成功添加道具: " + _loc3_ + " x" + _loc4_);
               return;
            }
            GoodsDataClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll._app.goods.GoodsData") as Class;
            GoodsDefineClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll._app.goods.define.GoodsDefine") as Class;
            GoodsAdditClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll._app.goods.GoodsAddit") as Class;
            if(GoodsDataClass && GoodsDefineClass && GoodsAdditClass)
            {
               goodsData = new GoodsDataClass();
               goodsDefine = new GoodsDefineClass();
               goodsDefine.name = _loc3_;
               goodsDefine.defineLabel = _loc3_;
               goodsDefine.dataType = "things";
               goodsData.def = goodsDefine;
               goodsData.nowNum = parseInt(_loc4_);
               GoodsAdditClass.addByGoodsData(goodsData,true);
               Cc.log("成功添加道具: " + _loc3_ + " x" + _loc4_);
               return;
            }
            playerData = this.getPlayerData();
            if(playerData && playerData.thingsBag)
            {
               playerData.thingsBag.addDataByName(_loc3_,parseInt(_loc4_));
               Cc.log("成功添加道具到背包: " + _loc3_ + " x" + _loc4_);
               return;
            }
            Cc.log("所有方法都失败了，请检查道具名称是否正确");
         }
         catch(error:Error)
         {
            Cc.log("添加道具失败: " + error.message);
         }
      }

      private function parseParams(param1:String) : Array
      {
         var params:Array = param1.split(" ");
         if(params.length < 2)
         {
            return ["", "1"];
         }
         return [params[0], params[1]];
      }

      private function getPlayerData() : *
      {
         try
         {
            if(this._PlayerDataClass)
            {
               return this._PlayerDataClass.getInstance();
            }
            var PlayerDataClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.player.PlayerData") as Class;
            if(PlayerDataClass)
            {
               this._PlayerDataClass = PlayerDataClass;
               return PlayerDataClass.getInstance();
            }
         }
         catch(error:Error)
         {
            Cc.log("获取玩家数据失败: " + error.message);
         }
         return null;
      }

      private function preloadAllClasses() : void
      {
         try
         {
            this._GamingClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            this._GiftAdditClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.GiftAddit") as Class;
            this._GiftAddDefineClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.define.GiftAddDefine") as Class;
            this._PlayerDataClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.player.PlayerData") as Class;
            this._ArmsDataClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.arms.ArmsData") as Class;
            this._EquipDataClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.equip.EquipData") as Class;
            this._ThingsDataClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.things.ThingsData") as Class;
            this._GameArmsCtrlClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("ctrl.GameArmsCtrl") as Class;
            this._UIShowClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("ui.UIShow") as Class;
            this._UIOrderClass = this.l.contentLoaderInfo.applicationDomain.getDefinition("ui.UIOrder") as Class;
            this.gameFullyLoaded = true;
            Cc.log("所有类预加载完成");
         }
         catch(error:Error)
         {
            Cc.log("预加载类失败: " + error.message);
         }
      }

      private function sound_s(param1:String) : void
      {
         try
         {
            var soundTransform:SoundTransform = SoundMixer.soundTransform;
            if(soundTransform.volume > 0)
            {
               soundTransform.volume = 0;
               Cc.log("已静音");
            }
            else
            {
               soundTransform.volume = 1;
               Cc.log("已取消静音");
            }
            SoundMixer.soundTransform = soundTransform;
         }
         catch(error:Error)
         {
            Cc.log("音效控制失败: " + error.message);
         }
      }
   }